// 历史记录模型图标测试脚本
// 在浏览器控制台中运行此脚本来测试功能

// 模拟历史记录数据
const testHistoryData = [
    {
        id: 'test1',
        timestamp: new Date('2025-08-05T08:12:00'),
        service: 'baidu',
        model: 'general_basic',
        mode: '文字',
        result: '这是一个百度OCR识别的测试结果',
        preview: '这是一个百度OCR识别的测试结果'
    },
    {
        id: 'test2',
        timestamp: new Date('2025-08-05T08:10:00'),
        service: 'openai',
        model: 'gpt-4-vision-preview',
        mode: '文字',
        result: 'OpenAI GPT-4 Vision识别的文本内容',
        preview: 'OpenAI GPT-4 Vision识别的文本内容'
    },
    {
        id: 'test3',
        timestamp: new Date('2025-08-05T08:08:00'),
        service: 'anthropic',
        model: 'claude-3-sonnet-20240229',
        mode: '文字',
        result: 'Claude 3 Sonnet识别的文本内容',
        preview: 'Claude 3 Sonnet识别的文本内容'
    },
    {
        id: 'test4',
        timestamp: new Date('2025-08-05T08:06:00'),
        service: 'zhipu',
        model: 'glm-4v',
        mode: '文字',
        result: '智谱AI GLM-4V识别的文本内容',
        preview: '智谱AI GLM-4V识别的文本内容'
    },
    {
        id: 'test5',
        timestamp: new Date('2025-08-05T08:04:00'),
        service: 'google',
        model: 'gemini-pro-vision',
        mode: '文字',
        result: 'Google Gemini Pro Vision识别的文本内容',
        preview: 'Google Gemini Pro Vision识别的文本内容'
    }
];

// 模拟翻译历史记录数据
const testTranslateHistoryData = [
    {
        id: 'translate1',
        timestamp: new Date('2025-08-05T08:15:00'),
        service: 'baidu',
        model: 'traditional',
        type: 'text', // 文本翻译
        sourceText: 'Hello World',
        targetText: '你好世界',
        sourceLanguage: 'en',
        targetLanguage: 'zh',
        preview: 'Hello World'
    },
    {
        id: 'translate2',
        timestamp: new Date('2025-08-05T08:13:00'),
        service: 'anthropic',
        model: 'claude-3-sonnet-20240229',
        type: 'text', // 文本翻译
        sourceText: 'Good morning',
        targetText: '早上好',
        sourceLanguage: 'en',
        targetLanguage: 'zh',
        preview: 'Good morning'
    },
    {
        id: 'translate3',
        timestamp: new Date('2025-08-05T08:11:00'),
        service: 'tencent',
        model: 'image-translate',
        type: 'image', // 图片翻译
        sourceText: 'Welcome to our store',
        targetText: '欢迎来到我们的商店',
        sourceLanguage: 'en',
        targetLanguage: 'zh',
        preview: 'Welcome to our store'
    }
];

// 服务图标映射
const serviceIcons = {
    'baidu': `<svg height="1em" style="flex:none;line-height:1" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg"><title>BaiduCloud</title><path d="M21.715 5.61l-3.983 2.31a.903.903 0 01-.896 0L12.44 5.384a.903.903 0 00-.897 0L7.156 7.92a.903.903 0 01-.896 0L2.276 5.617 12.002 0l9.713 5.61z" fill="#5BCA87"></path><path d="M18.641 9.467a.89.89 0 00-.438.77v5.072a.896.896 0 01-.445.77l-4.428 2.51a.884.884 0 00-.445.777v4.607l4.429-2.536 5.31-3.047V7.157l-3.983 2.31z" fill="#EC5D3E"></path><path d="M10.98 18.941a.936.936 0 00-.305-.352l-4.429-2.516a.903.903 0 01-.431-.764v-5.078a.89.89 0 00-.452-.757l-.451-.26L1.38 7.158V18.39l5.311 3.047L11.126 24v-4.608a.881.881 0 00-.146-.45z" fill="#2464F5"></path></svg>`,
    
    'openai': `<svg fill="currentColor" fill-rule="evenodd" height="1em" style="flex:none;line-height:1" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg"><title>OpenAI</title><path d="M21.55 10.004a5.416 5.416 0 00-.478-4.501c-1.217-2.09-3.662-3.166-6.05-2.66A5.59 5.59 0 0010.831 1C8.39.995 6.224 2.546 5.473 4.838A5.553 5.553 0 001.76 7.496a5.487 5.487 0 00.691 6.5 5.416 5.416 0 00.477 4.502c1.217 2.09 3.662 3.165 6.05 2.66A5.586 5.586 0 0013.168 23c2.443.006 4.61-1.546 5.361-3.84a5.553 5.553 0 003.715-2.66 5.488 5.488 0 00-.693-6.497v.001zm-8.381 11.558a4.199 4.199 0 01-2.675-.954c.034-.018.093-.05.132-.074l4.44-2.53a.71.71 0 00.364-.623v-6.176l1.877 1.069c.02.01.033.029.036.05v5.115c-.003 2.274-1.87 4.118-4.174 4.123zM4.192 17.78a4.059 4.059 0 01-.498-2.763c.032.02.09.055.131.078l4.44 2.53c.225.13.504.13.73 0l5.42-3.088v2.138a.068.068 0 01-.027.057L9.9 19.288c-1.999 1.136-4.552.46-5.707-1.51h-.001zM3.023 8.216A4.15 4.15 0 015.198 6.41l-.002.151v5.06a.711.711 0 00.364.624l5.42 3.087-1.876 1.07a.067.067 0 01-.063.005l-4.489-2.559c-1.995-1.14-2.679-3.658-1.53-5.63h.001zm15.417 3.54l-5.42-3.088L14.896 7.6a.067.067 0 01.063-.006l4.489 2.557c1.998 1.14 2.683 3.662 1.529 5.633a4.163 4.163 0 01-2.174 1.807V12.38a.71.71 0 00-.363-.623zm1.867-2.773a6.04 6.04 0 00-.132-.078l-4.44-2.53a.731.731 0 00-.729 0l-5.42 3.088V7.325a.068.068 0 01.027-.057L14.1 4.713c2-1.137 4.555-.46 5.707 1.513.487.833.664 1.809.499 2.757h.001zm-11.741 3.81l-1.877-1.068a.065.065 0 01-.036-.051V6.559c.001-2.277 1.873-4.122 4.181-4.12.976 0 1.92.338 2.671.954-.034.018-.092.05-.131.073l-4.44 2.53a.71.71 0 00-.365.623l-.003 6.173v.002zm1.02-2.168L12 9.25l2.414 1.375v2.75L12 14.75l-2.415-1.375v-2.75z"></path></svg>`,
    
    'anthropic': `<svg height="1em" style="flex:none;line-height:1" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg"><title>Claude</title><path d="M4.709 15.955l4.72-2.647.08-.23-.08-.128H9.2l-.79-.048-2.698-.073-2.339-.097-2.266-.122-.571-.121L0 11.784l.055-.352.48-.321.686.06 1.52.103 2.278.158 1.652.097 2.449.255h.389l.055-.157-.134-.098-.103-.097-2.358-1.596-2.552-1.688-1.336-.972-.724-.491-.364-.462-.158-1.008.656-.722.881.06.225.061.893.686 1.908 1.476 2.491 1.833.365.304.145-.103.019-.073-.164-.274-1.355-2.446-1.446-2.49-.644-1.032-.17-.619a2.97 2.97 0 01-.104-.729L6.283.134 6.696 0l.996.134.42.364.62 1.414 1.002 2.229 1.555 3.03.456.898.243.832.091.255h.158V9.01l.128-1.706.237-2.095.23-2.695.08-.76.376-.91.747-.492.584.28.48.685-.067.444-.286 1.851-.559 2.903-.364 1.942h.212l.243-.242.985-1.306 1.652-2.064.73-.82.85-.904.547-.431h1.033l.76 1.129-.34 1.166-1.064 1.347-.881 1.142-1.264 1.7-.79 1.36.073.11.188-.02 2.856-.606 1.543-.28 1.841-.315.833.388.091.395-.328.807-1.969.486-2.309.462-3.439.813-.042.03.049.061 1.549.146.662.036h1.622l3.02.225.79.522.474.638-.079.485-1.215.62-1.64-.389-3.829-.91-1.312-.329h-.182v.11l1.093 1.068 2.006 1.81 2.509 2.33.127.578-.322.455-.34-.049-2.205-1.657-.851-.747-1.926-1.62h-.128v.17l.444.649 2.345 3.521.122 1.08-.17.353-.608.213-.668-.122-1.374-1.925-1.415-2.167-1.143-1.943-.14.08-.674 7.254-.316.37-.729.28-.607-.461-.322-.747.322-1.476.389-1.924.315-1.53.286-1.9.17-.632-.012-.042-.14.018-1.434 1.967-2.18 2.945-1.726 1.845-.414.164-.717-.37.067-.662.401-.589 2.388-3.036 1.44-1.882.93-1.086-.006-.158h-.055L4.132 18.56l-1.13.146-.487-.456.061-.746.231-.243 1.908-1.312-.006.006z" fill="#D97757" fill-rule="nonzero"></path></svg>`,
    
    'zhipu': `<svg height="1em" style="flex:none;line-height:1" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg"><title>Zhipu</title><path d="M11.991 23.503a.24.24 0 00-.************* 0 *************.24 0 00.245-.249.24.24 0 00-.22-.247l-.025-.001z" fill="#3859FF" fill-rule="nonzero"></path></svg>`,
    
    'google': `<svg height="1em" style="flex:none;line-height:1" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg"><title>Gemini</title><defs><linearGradient id="google-gradient" x1="0%" x2="68.73%" y1="100%" y2="30.395%"><stop offset="0%" stop-color="#1C7DFF"></stop><stop offset="52.021%" stop-color="#1C69FF"></stop><stop offset="100%" stop-color="#F0DCD6"></stop></linearGradient></defs><path d="M12 24A14.304 14.304 0 000 12 14.304 14.304 0 0012 0a14.305 14.305 0 0012 12 14.305 14.305 0 00-12 12" fill="url(#google-gradient)" fill-rule="nonzero"></path></svg>`
};

// 翻译类型图标映射
const translateTypeIcons = {
    'text': `<svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="m5 8 6 6"/>
        <path d="m4 14 6-6 2-3"/>
        <path d="M2 5h12"/>
        <path d="M7 2h1"/>
        <path d="m22 22-5-10-5 10"/>
        <path d="M14 18h6"/>
    </svg>`,
    'image': `<svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M21 12.17V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h6"/>
        <path d="m6 21 5-5"/>
        <circle cx="9" cy="9" r="2"/>
    </svg>`
};

// 格式化时间
function formatTime(timestamp) {
    const date = new Date(timestamp);
    return `${date.getMonth() + 1}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
}

// 生成历史记录项HTML
function generateHistoryItemHTML(history, type = 'ocr') {
    const timeStr = formatTime(history.timestamp);
    const preview = history.preview || history.result || history.sourceText || '';
    const icon = serviceIcons[history.service] || '';
    const iconHtml = icon ? `<span class="history-model-icon">${icon}</span>` : '';

    // 获取翻译类型图标（仅对翻译类别生效）
    const translateTypeIcon = (type === 'translate') ? getTranslateTypeIcon(history.type) : '';
    const translateTypeIconHtml = translateTypeIcon ? `<span class="history-translate-type-icon">${translateTypeIcon}</span>` : '';

    return `
        <div class="history-item" data-id="${history.id}" data-type="${type}" onclick="selectHistoryItem('${history.id}', '${type}')">
            <div class="history-item-content">
                <div class="history-preview">${preview}</div>
                <div class="history-meta-line">
                    ${iconHtml}
                    ${translateTypeIconHtml}
                    <span class="history-time">${timeStr}</span>
                </div>
            </div>
            <button class="history-delete-btn" data-id="${history.id}" title="删除记录">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M18 6L6 18"/>
                    <path d="M6 6l12 12"/>
                </svg>
            </button>
        </div>
    `;
}

// 获取翻译类型图标
function getTranslateTypeIcon(type) {
    const translateType = type || 'text';
    return translateTypeIcons[translateType] || translateTypeIcons['text'];
}

// 选择历史记录项
function selectHistoryItem(id, type) {
    // 移除所有选中状态
    document.querySelectorAll('.history-item').forEach(item => {
        item.classList.remove('active');
    });

    // 添加选中状态
    const selectedItem = document.querySelector(`[data-id="${id}"]`);
    if (selectedItem) {
        selectedItem.classList.add('active');
    }

    // 显示详情
    showHistoryDetail(id, type);
}

// 显示历史记录详情
function showHistoryDetail(id, type) {
    const allHistories = [...testHistoryData, ...testTranslateHistoryData];
    const history = allHistories.find(h => h.id === id);

    if (!history) return;

    const resultHeader = document.getElementById('result-header');
    const detailContent = document.getElementById('detail-content');

    // 更新头部
    if (resultHeader) {
        updateResultHeader(resultHeader, history, type);
        resultHeader.style.display = 'flex';
    }

    // 更新内容
    if (detailContent) {
        const content = type === 'ocr' ?
            (history.result || '识别结果') :
            `原文：${history.sourceText || ''}\n译文：${history.targetText || ''}`;

        detailContent.innerHTML = `
            <div style="text-align: left;">
                <h3>详细内容</h3>
                <pre style="white-space: pre-wrap; background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 10px 0;">${content}</pre>
            </div>
        `;
    }
}

// 更新结果头部
function updateResultHeader(resultHeader, history, type) {
    if (!resultHeader || !history) return;

    const icon = serviceIcons[history.service] || '';
    const serviceName = getServiceDisplayName(history.service);
    const timeStr = formatTime(history.timestamp);

    const iconHtml = icon ? `<span class="result-header-icon">${icon}</span>` : '';
    const typeText = type === 'ocr' ? 'OCR识别' : '翻译';

    resultHeader.innerHTML = `
        <div class="result-header-content">
            ${iconHtml}
            <div class="result-header-info">
                <div class="result-header-title">${typeText} - ${serviceName}</div>
                <div class="result-header-time">${timeStr}</div>
            </div>
        </div>
    `;
}

// 获取服务显示名称
function getServiceDisplayName(service) {
    const names = {
        baidu: '百度智能云',
        openai: 'OpenAI',
        anthropic: 'Anthropic',
        zhipu: '智谱AI',
        google: 'Google Gemini',
        tencent: '腾讯云',
        aliyun: '阿里云'
    };
    return names[service] || service;
}

// 测试函数：渲染OCR历史记录
function testOCRHistory() {
    const historyList = document.getElementById('history-list');
    if (!historyList) {
        console.error('找不到历史记录列表元素');
        return;
    }
    
    const historyHTML = testHistoryData.map(history => generateHistoryItemHTML(history, 'ocr')).join('');
    historyList.innerHTML = historyHTML;
    console.log('OCR历史记录已渲染，包含模型图标');
}

// 测试函数：渲染翻译历史记录
function testTranslateHistory() {
    const historyList = document.getElementById('history-list');
    if (!historyList) {
        console.error('找不到历史记录列表元素');
        return;
    }
    
    const historyHTML = testTranslateHistoryData.map(history => generateHistoryItemHTML(history, 'translate')).join('');
    historyList.innerHTML = historyHTML;
    console.log('翻译历史记录已渲染，包含模型图标');
}

// 测试函数：混合历史记录
function testMixedHistory() {
    const historyList = document.getElementById('history-list');
    if (!historyList) {
        console.error('找不到历史记录列表元素');
        return;
    }
    
    const ocrHTML = testHistoryData.slice(0, 3).map(history => generateHistoryItemHTML(history, 'ocr')).join('');
    const translateHTML = testTranslateHistoryData.map(history => generateHistoryItemHTML(history, 'translate')).join('');
    
    historyList.innerHTML = ocrHTML + translateHTML;
    console.log('混合历史记录已渲染，包含模型图标');
}

// 导出测试函数到全局作用域
window.testOCRHistory = testOCRHistory;
window.testTranslateHistory = testTranslateHistory;
window.testMixedHistory = testMixedHistory;
window.selectHistoryItem = selectHistoryItem;
window.showHistoryDetail = showHistoryDetail;
window.updateResultHeader = updateResultHeader;
window.getServiceDisplayName = getServiceDisplayName;
window.getTranslateTypeIcon = getTranslateTypeIcon;

console.log('历史记录图标测试脚本已加载');
console.log('可用的测试函数:');
console.log('- testOCRHistory(): 测试OCR历史记录');
console.log('- testTranslateHistory(): 测试翻译历史记录');
console.log('- testMixedHistory(): 测试混合历史记录');
